import laspy
import numpy as np
import os

def convert_las_to_npy_full(las_path, output_dir=None):
    if not os.path.isfile(las_path):
        raise FileNotFoundError(f"File not found: {las_path}")

    print(f"🔍 Reading file: {las_path}")
    las = laspy.read(las_path)

    print("\n📌 LAS Header Information:")
    print(f"  - Version: {las.header.version}")
    print(f"  - Point format: {las.header.point_format}")
    print(f"  - Number of points: {len(las.points)}")
    print(f"  - Scale factors: {las.header.scales}")
    print(f"  - Offsets: {las.header.offsets}")
    print(f"  - Bounding box: {las.header.mins} to {las.header.maxs}")
    print(f"  - CRS (if available): {las.header.parse_crs()}")

    # Extract core point attributes
    x = las.x
    y = las.y
    z = las.z
    intensity = las.intensity
    return_num = las.return_number
    num_returns = las.number_of_returns
    classification = las.classification

    # Stack into one array
    points = np.vstack((x, y, z, intensity, return_num, num_returns, classification)).T
    print(f"\n✅ Extracted point attributes with shape: {points.shape}")
    print("    Columns: [X, Y, Z, Intensity, ReturnNumber, NumReturns, Classification]")

    # Save to .npy
    output_name = os.path.splitext(os.path.basename(las_path))[0] + "_full.npy"
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, output_name)
    else:
        output_path = os.path.join(os.path.dirname(las_path), output_name)

    np.save(output_path, points)
    print(f"\n💾 Saved structured point cloud to: {output_path}")

    return output_path

# Example usage
if __name__ == "__main__":
    las_file_path = r"D:\review_sample\FusedLidar_Lidar1_UTM32N_filtered.las"  # Replace with your file path
    convert_las_to_npy_full(las_file_path)
