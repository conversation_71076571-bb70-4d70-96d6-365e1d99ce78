import numpy as np
import matplotlib.pyplot as plt
import os

def generate_bev_with_intensity(npy_file, resolution=1.0, z_threshold=None):
    print(f"📂 Loading point cloud: {npy_file}")
    data = np.load(npy_file)

    x, y, z = data[:, 0], data[:, 1], data[:, 2]
    intensity = data[:, 3]

    if z_threshold:
        mask = z > z_threshold
        x, y, z, intensity = x[mask], y[mask], z[mask], intensity[mask]

    x -= x.min()
    y -= y.min()

    x_grid = (x / resolution).astype(np.int32)
    y_grid = (y / resolution).astype(np.int32)

    grid_size_x = x_grid.max() + 1
    grid_size_y = y_grid.max() + 1

    print(f"🗺 BEV size: ({grid_size_y}, {grid_size_x}) with resolution {resolution} m/pixel")

    # Create grid and fill with max intensity
    bev = np.zeros((grid_size_y, grid_size_x), dtype=np.uint8)

    for i in range(len(x_grid)):
        xi, yi = x_grid[i], y_grid[i]
        if 0 <= yi < grid_size_y and 0 <= xi < grid_size_x:
            bev[yi, xi] = max(bev[yi, xi], intensity[i])

    # Flip vertically for typical BEV orientation
    bev = np.flipud(bev)

    output_path = os.path.splitext(npy_file)[0] + f"_bev_{resolution}m_intensity.png"
    plt.imsave(output_path, bev, cmap='gray')
    print(f"✅ Saved intensity BEV to: {output_path}")

    return output_path

# Example usage
if __name__ == "__main__":
    npy_path = "FusedLidar_Lidar1_UTM32N_filtered_full.npy"  # Update if needed
    generate_bev_with_intensity(npy_path, resolution=2.0, z_threshold=1.0)
