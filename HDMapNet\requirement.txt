addict==2.4.0
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
asttokens==2.0.5
attrs==21.4.0
backcall==0.2.0
black==22.1.0
bleach==4.1.0
cachetools==5.0.0
certifi==2021.10.8
cffi==1.15.0
click==8.0.4
cycler==0.11.0
debugpy==1.5.1
decorator==5.1.1
defusedxml==0.7.1
descartes==1.1.0
efficientnet-pytorch==0.7.1
entrypoints==0.4
executing==0.8.2
fire==0.4.0
fonttools==4.29.1
importlib-resources==5.4.0
#ipdb==0.13.9
ipykernel==6.9.1
ipython==8.0.1
ipython-genutils==0.2.0
#ipywidgets==7.6.5
jedi==0.18.1
Jinja2==3.0.3
joblib==1.1.0
jsonschema==4.4.0
#jupyter==1.0.0
#jupyter-client==7.1.2
#jupyter-console==6.4.0
#jupyter-core==4.9.2
#jupyterlab-pygments==0.1.2
#jupyterlab-widgets==1.0.2
kiwisolver==1.3.2
MarkupSafe==2.1.0
matplotlib==3.5.1
matplotlib-inline==0.1.3
mistune==0.8.4
# mmcv==1.4.5
mypy-extensions==0.4.3
nbclient==0.5.11
nbconvert==6.4.2
nbformat==5.1.3
nest-asyncio==1.5.4
#notebook==6.4.8
numpy==1.22.2
nuscenes-devkit==1.1.9
opencv-python==********
packaging==21.3
pandocfilters==1.5.0
parso==0.8.3
pathspec==0.9.0
pexpect==4.8.0
pickleshare==0.7.5
Pillow==9.0.1
platformdirs==2.5.1
prometheus-client==0.13.1
prompt-toolkit==3.0.28
protobuf==3.19.4
ptyprocess==0.7.0
pure-eval==0.2.2
pycocotools==2.0.4
pycparser==2.21
Pygments==2.11.2
pyparsing==3.0.7
pyquaternion==0.9.9
pyrsistent==0.18.1
python-dateutil==2.8.2
PyYAML==6.0
#pyzmq==22.3.0
qtconsole==5.2.2
QtPy==2.0.1
scikit-learn==1.0.2
scipy==1.8.0
Send2Trash==1.8.0
Shapely==1.8.1.post1
six==1.16.0
stack-data==0.2.0
tensorboardX==2.4.1
termcolor==1.1.0
#terminado==0.13.1
testpath==0.5.0
threadpoolctl==3.1.0
toml==0.10.2
tomli==2.0.1
#torch==1.10.2+cu113
#torch-scatter==2.0.9
#torchaudio==0.10.2+cu113
#torchvision==0.11.3+cu113
tornado==6.1
tqdm==4.62.3
traitlets==5.1.1
typing_extensions==4.1.1
wcwidth==0.2.5
webencodings==0.5.1
#widgetsnbextension==3.5.2
yapf==0.32.0
zipp==3.7.0
