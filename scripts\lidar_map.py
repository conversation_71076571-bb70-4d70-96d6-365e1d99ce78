import torch
import numpy as np
import os
from models.HDMapNet import get_model
from utils.lidar_encoders import bev_encoding_from_pointcloud_tensor

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def load_point_cloud_npy(npy_file):
    data = np.load(npy_file)  # Shape: (N, 7)
    points = data[:, :3]  # x, y, z
    return torch.tensor(points, dtype=torch.float32)

def run_hdmapnet_lidar_only(npy_file):
    print(f"📂 Loading point cloud from: {npy_file}")
    pointcloud = load_point_cloud_npy(npy_file)  # shape (N, 3)
    pointcloud = pointcloud.unsqueeze(0)  # shape (1, N, 3)

    # Convert point cloud to BEV encoding used by HDMapNet
    print("🛠 Generating BEV features from point cloud...")
    bev_tensor = bev_encoding_from_pointcloud_tensor(pointcloud)  # shape (1, C, H, W)

    # Load pretrained model
    print("📦 Loading HDMapNet model...")
    model = get_model()
    model.load_state_dict(torch.load("ckpt/HDMapNet_model.pth", map_location=device))
    model.to(device)
    model.eval()

    # Run inference
    with torch.no_grad():
        output = model({"lidar_bev": bev_tensor.to(device)})

    # Save results
    for key, val in output.items():
        npy_out = val.squeeze().cpu().numpy()
        np.save(f"{key}_output.npy", npy_out)
        print(f"✅ Saved: {key}_output.npy")

if __name__ == "__main__":
    npy_file = r"D:\review_sample\FusedLidar_Lidar1_UTM32N_filtered_full.npy"  # Your point cloud
    run_hdmapnet_lidar_only(npy_file)
